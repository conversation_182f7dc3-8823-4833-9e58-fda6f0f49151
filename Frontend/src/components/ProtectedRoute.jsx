import React, { useContext, useEffect, useState } from 'react';
import { ShopContext } from '../context/ShopContextProvider';
import { Navigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const ProtectedRoute = ({ children, requireAdmin = false }) => {
    const { token, user, loading, fetchUserProfile } = useContext(ShopContext);
    const [isChecking, setIsChecking] = useState(true);

    useEffect(() => {
        const checkAuth = async () => {
            if (!token) {
                setIsChecking(false);
                return;
            }

            if (!user) {
                // If we have a token but no user data, fetch it
                await fetchUserProfile();
            }
            
            setIsChecking(false);
        };

        checkAuth();
    }, [token, user, fetchUserProfile]);

    // Show loading while checking authentication
    if (isChecking || loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-[--color2]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    // Check if user is authenticated
    if (!token) {
        toast.error('Please login to access this page');
        return <Navigate to="/Login" replace />;
    }

    // Check if admin access is required
    if (requireAdmin && user?.role !== 'admin') {
        toast.error('Access denied. Admin privileges required.');
        return <Navigate to="/" replace />;
    }

    return children;
};

export default ProtectedRoute;
