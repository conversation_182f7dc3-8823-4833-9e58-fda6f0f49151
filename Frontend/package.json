{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@heroicons/react": "^2.2.0", "@mui/material": "^7.1.2", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.7.0", "@stripe/stripe-js": "^7.3.0", "axios": "^1.9.0", "express-session": "^1.18.1", "firebase": "^11.6.1", "gsap": "^3.13.0", "motion": "^12.20.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-rating-stars-component": "^2.2.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-spring": "^10.0.1", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.1.0"}, "eslintConfig": {"extends": ["react-app"]}}